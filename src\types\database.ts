// Database types for Supabase integration

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          subscription_tier: 'free' | 'pro' | 'business' | 'enterprise'
          subscription_status: 'active' | 'canceled' | 'past_due' | 'trialing' | null
          stripe_customer_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          subscription_tier?: 'free' | 'pro' | 'business' | 'enterprise'
          subscription_status?: 'active' | 'canceled' | 'past_due' | 'trialing' | null
          stripe_customer_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          subscription_tier?: 'free' | 'pro' | 'business' | 'enterprise'
          subscription_status?: 'active' | 'canceled' | 'past_due' | 'trialing' | null
          stripe_customer_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      projects: {
        Row: {
          id: string
          user_id: string
          name: string
          description: string | null
          original_image_url: string
          mockup_image_url: string | null
          template_id: string
          settings: Json
          status: 'draft' | 'processing' | 'completed' | 'failed'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          description?: string | null
          original_image_url: string
          mockup_image_url?: string | null
          template_id: string
          settings?: Json
          status?: 'draft' | 'processing' | 'completed' | 'failed'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          description?: string | null
          original_image_url?: string
          mockup_image_url?: string | null
          template_id?: string
          settings?: Json
          status?: 'draft' | 'processing' | 'completed' | 'failed'
          created_at?: string
          updated_at?: string
        }
      }
      templates: {
        Row: {
          id: string
          name: string
          description: string
          category: 'clothing' | 'bottles' | 'packaging' | 'electronics' | 'books' | 'stationery' | 'accessories' | 'home-decor' | 'other'
          preview_image_url: string
          tags: string[]
          is_premium: boolean
          settings: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description: string
          category: 'clothing' | 'bottles' | 'packaging' | 'electronics' | 'books' | 'stationery' | 'accessories' | 'home-decor' | 'other'
          preview_image_url: string
          tags?: string[]
          is_premium?: boolean
          settings?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string
          category?: 'clothing' | 'bottles' | 'packaging' | 'electronics' | 'books' | 'stationery' | 'accessories' | 'home-decor' | 'other'
          preview_image_url?: string
          tags?: string[]
          is_premium?: boolean
          settings?: Json
          created_at?: string
          updated_at?: string
        }
      }
      usage_tracking: {
        Row: {
          id: string
          user_id: string
          action: 'mockup_generated' | 'image_uploaded' | 'project_created'
          metadata: Json | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          action: 'mockup_generated' | 'image_uploaded' | 'project_created'
          metadata?: Json | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          action?: 'mockup_generated' | 'image_uploaded' | 'project_created'
          metadata?: Json | null
          created_at?: string
        }
      }
      generations: {
        Row: {
          id: string
          project_id: string
          user_id: string
          prompt: string
          negative_prompt: string | null
          settings: Json
          status: 'queued' | 'processing' | 'completed' | 'failed'
          result_url: string | null
          error_message: string | null
          created_at: string
          completed_at: string | null
        }
        Insert: {
          id?: string
          project_id: string
          user_id: string
          prompt: string
          negative_prompt?: string | null
          settings?: Json
          status?: 'queued' | 'processing' | 'completed' | 'failed'
          result_url?: string | null
          error_message?: string | null
          created_at?: string
          completed_at?: string | null
        }
        Update: {
          id?: string
          project_id?: string
          user_id?: string
          prompt?: string
          negative_prompt?: string | null
          settings?: Json
          status?: 'queued' | 'processing' | 'completed' | 'failed'
          result_url?: string | null
          error_message?: string | null
          created_at?: string
          completed_at?: string | null
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      subscription_tier: 'free' | 'pro' | 'business' | 'enterprise'
      subscription_status: 'active' | 'canceled' | 'past_due' | 'trialing'
      project_status: 'draft' | 'processing' | 'completed' | 'failed'
      template_category: 'clothing' | 'bottles' | 'packaging' | 'electronics' | 'books' | 'stationery' | 'accessories' | 'home-decor' | 'other'
      generation_status: 'queued' | 'processing' | 'completed' | 'failed'
      usage_action: 'mockup_generated' | 'image_uploaded' | 'project_created'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
