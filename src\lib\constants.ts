// Constants for MockMaster Pro

export const APP_CONFIG = {
  name: 'MockMaster Pro',
  description: 'AI-powered product mockup generation platform',
  version: '1.0.0',
  url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
} as const;

export const SUBSCRIPTION_LIMITS = {
  free: {
    mockupsPerMonth: 5,
    maxFileSize: 5 * 1024 * 1024, // 5MB
    features: ['basic-templates', 'watermarked-exports'],
    exportFormats: ['jpg', 'png'],
    maxResolution: '1080p',
  },
  pro: {
    mockupsPerMonth: 100,
    maxFileSize: 10 * 1024 * 1024, // 10MB
    features: ['all-templates', 'hd-exports', 'priority-support'],
    exportFormats: ['jpg', 'png', 'pdf'],
    maxResolution: '4k',
  },
  business: {
    mockupsPerMonth: -1, // unlimited
    maxFileSize: 20 * 1024 * 1024, // 20MB
    features: ['all-templates', '4k-exports', 'api-access', 'dedicated-support'],
    exportFormats: ['jpg', 'png', 'pdf', 'svg'],
    maxResolution: '8k',
  },
  enterprise: {
    mockupsPerMonth: -1, // unlimited
    maxFileSize: 50 * 1024 * 1024, // 50MB
    features: ['all-templates', '8k-exports', 'api-access', 'dedicated-support', 'custom-templates'],
    exportFormats: ['jpg', 'png', 'pdf', 'svg', 'psd'],
    maxResolution: '8k',
  },
} as const;

export const TEMPLATE_CATEGORIES = [
  { id: 'clothing', name: 'Clothing & Apparel', icon: '👕' },
  { id: 'bottles', name: 'Bottles & Containers', icon: '🍾' },
  { id: 'packaging', name: 'Packaging & Boxes', icon: '📦' },
  { id: 'electronics', name: 'Electronics & Gadgets', icon: '📱' },
  { id: 'books', name: 'Books & Publications', icon: '📚' },
  { id: 'stationery', name: 'Stationery & Office', icon: '✏️' },
  { id: 'accessories', name: 'Accessories & Jewelry', icon: '💍' },
  { id: 'home-decor', name: 'Home & Decor', icon: '🏠' },
  { id: 'other', name: 'Other', icon: '🎨' },
] as const;

export const SUPPORTED_FILE_TYPES = {
  images: ['.jpg', '.jpeg', '.png', '.webp'],
  exports: ['.jpg', '.png', '.pdf', '.svg', '.psd'],
} as const;

export const GENERATION_SETTINGS = {
  default: {
    steps: 30,
    guidance: 7.5,
    width: 1024,
    height: 1024,
  },
  quality: {
    draft: { steps: 20, guidance: 5 },
    standard: { steps: 30, guidance: 7.5 },
    high: { steps: 50, guidance: 10 },
    ultra: { steps: 100, guidance: 12 },
  },
} as const;

export const API_ENDPOINTS = {
  auth: {
    login: '/api/auth/login',
    register: '/api/auth/register',
    logout: '/api/auth/logout',
    profile: '/api/auth/profile',
  },
  projects: {
    list: '/api/projects',
    create: '/api/projects',
    get: (id: string) => `/api/projects/${id}`,
    update: (id: string) => `/api/projects/${id}`,
    delete: (id: string) => `/api/projects/${id}`,
  },
  templates: {
    list: '/api/templates',
    get: (id: string) => `/api/templates/${id}`,
    categories: '/api/templates/categories',
  },
  generation: {
    create: '/api/generation',
    status: (id: string) => `/api/generation/${id}`,
    cancel: (id: string) => `/api/generation/${id}/cancel`,
  },
  upload: {
    image: '/api/upload/image',
    presigned: '/api/upload/presigned',
  },
  subscription: {
    plans: '/api/subscription/plans',
    current: '/api/subscription/current',
    create: '/api/subscription/create',
    cancel: '/api/subscription/cancel',
    usage: '/api/subscription/usage',
  },
} as const;

export const STORAGE_KEYS = {
  theme: 'mockmaster-theme',
  recentProjects: 'mockmaster-recent-projects',
  preferences: 'mockmaster-preferences',
  draftProject: 'mockmaster-draft-project',
} as const;

export const THEME_CONFIG = {
  colors: {
    primary: {
      50: '#eef2ff',
      100: '#e0e7ff',
      500: '#6366f1',
      600: '#5b21b6',
      900: '#312e81',
    },
    secondary: {
      50: '#fffbeb',
      100: '#fef3c7',
      500: '#f59e0b',
      600: '#d97706',
      900: '#78350f',
    },
  },
  fonts: {
    sans: ['Inter', 'system-ui', 'sans-serif'],
    mono: ['JetBrains Mono', 'monospace'],
  },
} as const;

export const ANIMATION_DURATIONS = {
  fast: 150,
  normal: 300,
  slow: 500,
} as const;

export const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

export const ERROR_MESSAGES = {
  generic: 'Something went wrong. Please try again.',
  network: 'Network error. Please check your connection.',
  unauthorized: 'You are not authorized to perform this action.',
  fileSize: 'File size exceeds the maximum limit.',
  fileType: 'File type is not supported.',
  quotaExceeded: 'You have exceeded your monthly quota.',
  processing: 'Your mockup is being processed. Please wait.',
} as const;

export const SUCCESS_MESSAGES = {
  projectCreated: 'Project created successfully!',
  projectUpdated: 'Project updated successfully!',
  projectDeleted: 'Project deleted successfully!',
  mockupGenerated: 'Mockup generated successfully!',
  settingsSaved: 'Settings saved successfully!',
} as const;
