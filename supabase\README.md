# Supabase Setup for MockMaster Pro

This directory contains the database schema and configuration for MockMaster Pro's Supabase backend.

## Quick Setup

### 1. Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and create a new project
2. Choose a database password and region
3. Wait for the project to be created

### 2. Configure Environment Variables

Copy the project URL and anon key from your Supabase dashboard:

```bash
# Add to your .env.local file
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### 3. Run the Database Schema

1. Open the SQL Editor in your Supabase dashboard
2. Copy and paste the contents of `schema.sql`
3. Run the query to create all tables, functions, and policies

### 4. Set Up Storage Buckets

Create the following storage buckets in your Supabase dashboard:

1. **original-images** - For user uploaded product images
   - Public: No
   - File size limit: 10MB
   - Allowed MIME types: `image/jpeg, image/png, image/webp`

2. **mockup-images** - For generated mockup images
   - Public: Yes
   - File size limit: 50MB
   - Allowed MIME types: `image/jpeg, image/png`

3. **template-previews** - For template preview images
   - Public: Yes
   - File size limit: 5MB
   - Allowed MIME types: `image/jpeg, image/png, image/webp`

4. **avatars** - For user profile pictures
   - Public: Yes
   - File size limit: 2MB
   - Allowed MIME types: `image/jpeg, image/png, image/webp`

### 5. Configure Storage Policies

For each bucket, set up the appropriate RLS policies:

#### original-images policies:
```sql
-- Users can upload their own images
CREATE POLICY "Users can upload own images" ON storage.objects
FOR INSERT WITH CHECK (auth.uid()::text = (storage.foldername(name))[1]);

-- Users can view their own images
CREATE POLICY "Users can view own images" ON storage.objects
FOR SELECT USING (auth.uid()::text = (storage.foldername(name))[1]);

-- Users can delete their own images
CREATE POLICY "Users can delete own images" ON storage.objects
FOR DELETE USING (auth.uid()::text = (storage.foldername(name))[1]);
```

#### mockup-images policies:
```sql
-- Users can upload their own mockups
CREATE POLICY "Users can upload own mockups" ON storage.objects
FOR INSERT WITH CHECK (auth.uid()::text = (storage.foldername(name))[1]);

-- Anyone can view mockups (for sharing)
CREATE POLICY "Anyone can view mockups" ON storage.objects
FOR SELECT USING (true);
```

#### template-previews policies:
```sql
-- Anyone can view template previews
CREATE POLICY "Anyone can view template previews" ON storage.objects
FOR SELECT USING (true);

-- Only service role can upload template previews
CREATE POLICY "Service can upload template previews" ON storage.objects
FOR INSERT WITH CHECK (true);
```

#### avatars policies:
```sql
-- Users can upload their own avatars
CREATE POLICY "Users can upload own avatars" ON storage.objects
FOR INSERT WITH CHECK (auth.uid()::text = (storage.foldername(name))[1]);

-- Anyone can view avatars
CREATE POLICY "Anyone can view avatars" ON storage.objects
FOR SELECT USING (true);

-- Users can update their own avatars
CREATE POLICY "Users can update own avatars" ON storage.objects
FOR UPDATE USING (auth.uid()::text = (storage.foldername(name))[1]);
```

## Database Schema Overview

### Tables

- **profiles** - User profiles extending auth.users
- **templates** - Mockup templates with categories and settings
- **projects** - User projects linking original images to templates
- **generations** - AI generation requests and results
- **usage_tracking** - Track user actions for billing and analytics

### Key Features

- **Row Level Security (RLS)** - Users can only access their own data
- **Automatic timestamps** - created_at and updated_at fields
- **Usage tracking** - Monitor user activity for subscription limits
- **File storage** - Organized buckets for different file types

## Development Tips

### Testing the Setup

1. Start your Next.js development server
2. Check the console for Supabase connection status
3. Try creating a user account to test the auth flow
4. Verify that the profile is created automatically

### Common Issues

1. **RLS Policies** - Make sure policies are set up correctly for each table
2. **Storage Buckets** - Ensure buckets exist and have proper policies
3. **Environment Variables** - Double-check your .env.local file
4. **CORS** - Add your domain to Supabase's allowed origins

### Useful SQL Queries

```sql
-- Check if user profiles are being created
SELECT * FROM profiles ORDER BY created_at DESC LIMIT 10;

-- View usage statistics
SELECT action, COUNT(*) as count 
FROM usage_tracking 
GROUP BY action;

-- Check project status distribution
SELECT status, COUNT(*) as count 
FROM projects 
GROUP BY status;
```

## Production Considerations

1. **Backup Strategy** - Set up automated backups
2. **Monitoring** - Enable logging and monitoring
3. **Performance** - Add indexes for frequently queried columns
4. **Security** - Review and audit RLS policies regularly
5. **Scaling** - Monitor database performance and upgrade as needed
