// Core types for MockMaster Pro

export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  subscription: SubscriptionTier;
  createdAt: Date;
  updatedAt: Date;
}

export interface Project {
  id: string;
  userId: string;
  name: string;
  description?: string;
  originalImageUrl: string;
  mockupImageUrl?: string;
  templateId: string;
  settings: MockupSettings;
  status: ProjectStatus;
  createdAt: Date;
  updatedAt: Date;
}

export interface Template {
  id: string;
  name: string;
  description: string;
  category: TemplateCategory;
  previewImageUrl: string;
  tags: string[];
  isPremium: boolean;
  settings: TemplateSettings;
  createdAt: Date;
}

export interface MockupSettings {
  background: BackgroundSettings;
  lighting: LightingSettings;
  positioning: PositioningSettings;
  effects: EffectSettings;
}

export interface BackgroundSettings {
  type: 'solid' | 'gradient' | 'image' | 'transparent';
  color?: string;
  gradientColors?: string[];
  imageUrl?: string;
  blur?: number;
}

export interface LightingSettings {
  intensity: number;
  direction: 'top' | 'bottom' | 'left' | 'right' | 'center';
  softness: number;
  shadows: boolean;
}

export interface PositioningSettings {
  x: number;
  y: number;
  scale: number;
  rotation: number;
}

export interface EffectSettings {
  brightness: number;
  contrast: number;
  saturation: number;
  blur: number;
}

export interface TemplateSettings {
  defaultBackground: BackgroundSettings;
  defaultLighting: LightingSettings;
  defaultPositioning: PositioningSettings;
  allowedAspectRatios: string[];
  maxImageSize: number;
}

export type SubscriptionTier = 'free' | 'pro' | 'business' | 'enterprise';

export type ProjectStatus = 'draft' | 'processing' | 'completed' | 'failed';

export type TemplateCategory = 
  | 'clothing'
  | 'bottles'
  | 'packaging'
  | 'electronics'
  | 'books'
  | 'stationery'
  | 'accessories'
  | 'home-decor'
  | 'other';

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface GenerationRequest {
  projectId: string;
  prompt: string;
  negativePrompt?: string;
  steps: number;
  guidance: number;
  seed?: number;
}

export interface GenerationResponse {
  id: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  imageUrl?: string;
  error?: string;
  estimatedTime?: number;
}
