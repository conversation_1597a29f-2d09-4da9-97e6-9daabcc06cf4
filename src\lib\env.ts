// Environment variables configuration and validation

import { z } from 'zod';

const envSchema = z.object({
  // App Configuration
  NEXT_PUBLIC_APP_URL: z.string().url().default('http://localhost:3000'),
  NEXT_PUBLIC_APP_NAME: z.string().default('MockMaster Pro'),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  
  // Supabase Configuration
  NEXT_PUBLIC_SUPABASE_URL: z.string().url().optional(),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().optional(),
  SUPABASE_SERVICE_ROLE_KEY: z.string().optional(),
  
  // Stable Diffusion XL API
  STABLE_DIFFUSION_API_KEY: z.string().optional(),
  STABLE_DIFFUSION_API_URL: z.string().url().default('https://api.stability.ai/v1'),
  
  // Stripe Configuration
  STRIPE_PUBLISHABLE_KEY: z.string().optional(),
  STRIPE_SECRET_KEY: z.string().optional(),
  STRIPE_WEBHOOK_SECRET: z.string().optional(),
  
  // File Upload Configuration
  NEXT_PUBLIC_MAX_FILE_SIZE: z.string().transform(Number).default('10485760'),
  NEXT_PUBLIC_ALLOWED_FILE_TYPES: z.string().default('image/jpeg,image/jpg,image/png,image/webp'),
  
  // Email Configuration (Optional)
  SMTP_HOST: z.string().optional(),
  SMTP_PORT: z.string().transform(Number).optional(),
  SMTP_USER: z.string().optional(),
  SMTP_PASS: z.string().optional(),
  FROM_EMAIL: z.string().email().optional(),
  
  // Analytics (Optional)
  NEXT_PUBLIC_GA_MEASUREMENT_ID: z.string().optional(),
  NEXT_PUBLIC_POSTHOG_KEY: z.string().optional(),
  
  // Development/Debug
  NEXT_PUBLIC_DEBUG: z.string().transform(val => val === 'true').default('false'),
});

// Validate environment variables
function validateEnv() {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    console.error('❌ Invalid environment variables:', error);
    throw new Error('Invalid environment variables');
  }
}

// Export validated environment variables
export const env = validateEnv();

// Helper functions for environment checks
export const isDevelopment = env.NODE_ENV === 'development';
export const isProduction = env.NODE_ENV === 'production';
export const isTest = env.NODE_ENV === 'test';

// Feature flags based on environment variables
export const features = {
  supabase: !!(env.NEXT_PUBLIC_SUPABASE_URL && env.NEXT_PUBLIC_SUPABASE_ANON_KEY),
  stableDiffusion: !!env.STABLE_DIFFUSION_API_KEY,
  stripe: !!(env.STRIPE_PUBLISHABLE_KEY && env.STRIPE_SECRET_KEY),
  email: !!(env.SMTP_HOST && env.SMTP_USER && env.SMTP_PASS),
  analytics: !!(env.NEXT_PUBLIC_GA_MEASUREMENT_ID || env.NEXT_PUBLIC_POSTHOG_KEY),
} as const;

// Configuration objects
export const config = {
  app: {
    name: env.NEXT_PUBLIC_APP_NAME,
    url: env.NEXT_PUBLIC_APP_URL,
    debug: env.NEXT_PUBLIC_DEBUG,
  },
  upload: {
    maxFileSize: env.NEXT_PUBLIC_MAX_FILE_SIZE,
    allowedTypes: env.NEXT_PUBLIC_ALLOWED_FILE_TYPES.split(','),
  },
  ai: {
    apiUrl: env.STABLE_DIFFUSION_API_URL,
    apiKey: env.STABLE_DIFFUSION_API_KEY,
  },
} as const;

// Runtime environment checks
export function requireEnvVar(name: string, value?: string): string {
  if (!value) {
    throw new Error(`Missing required environment variable: ${name}`);
  }
  return value;
}

// Log environment status (development only)
if (isDevelopment) {
  console.log('🔧 Environment Configuration:');
  console.log(`  - App: ${config.app.name} (${env.NODE_ENV})`);
  console.log(`  - URL: ${config.app.url}`);
  console.log(`  - Features:`);
  console.log(`    - Supabase: ${features.supabase ? '✅' : '❌'}`);
  console.log(`    - Stable Diffusion: ${features.stableDiffusion ? '✅' : '❌'}`);
  console.log(`    - Stripe: ${features.stripe ? '✅' : '❌'}`);
  console.log(`    - Email: ${features.email ? '✅' : '❌'}`);
  console.log(`    - Analytics: ${features.analytics ? '✅' : '❌'}`);
}
