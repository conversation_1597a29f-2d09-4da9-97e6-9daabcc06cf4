# MockMaster Pro

AI-powered product mockup generation platform that transforms 2D product images into photorealistic mockups using Stable Diffusion XL technology.

## Features

- 🤖 **AI-Powered Generation**: Leverage Stable Diffusion XL for photorealistic mockups
- 🎨 **Template Library**: Hundreds of professional templates for various product categories
- ⚙️ **Advanced Customization**: Fine-tune lighting, backgrounds, positioning, and scaling
- 💳 **Subscription Management**: Flexible pricing tiers with Stripe integration
- 🔐 **Authentication**: Secure user management with Supabase Auth
- 📱 **Responsive Design**: Mobile-first design with Tailwind CSS
- 🚀 **Modern Stack**: Built with Next.js 14+, TypeScript, and React 19

## Tech Stack

- **Framework**: Next.js 14+ with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **Backend**: Supabase (Database, Auth, Storage)
- **Payments**: Stripe
- **AI**: Stable Diffusion XL API
- **UI Components**: Radix UI primitives
- **State Management**: React hooks and context

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Supabase account
- Stripe account
- Stable Diffusion API access

### Installation

1. Clone the repository:
```bash
git clone https://github.com/your-username/mockmaster-pro.git
cd mockmaster-pro
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

4. Fill in your environment variables in `.env.local`

5. Run the development server:
```bash
npm run dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser

## Project Structure

```
src/
├── app/                 # Next.js App Router pages
├── components/          # React components
│   ├── ui/             # Reusable UI components
│   ├── layout/         # Layout components
│   ├── forms/          # Form components
│   └── mockup/         # Mockup-specific components
├── lib/                # Configuration and utilities
├── types/              # TypeScript type definitions
└── utils/              # Utility functions
```

## Environment Variables

See `.env.example` for all required environment variables. Key variables include:

- `NEXT_PUBLIC_SUPABASE_URL` - Your Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Supabase anonymous key
- `STABLE_DIFFUSION_API_KEY` - Stable Diffusion API key
- `STRIPE_PUBLISHABLE_KEY` - Stripe publishable key
- `STRIPE_SECRET_KEY` - Stripe secret key

## Development Workflow

1. **Phase 1**: Core Infrastructure ✅
   - Next.js project initialization
   - Tailwind CSS configuration
   - Project structure setup

2. **Phase 2**: Authentication & Database
   - Supabase integration
   - User authentication
   - Database schema

3. **Phase 3**: Core Features
   - Image upload system
   - Template gallery
   - AI mockup generation

4. **Phase 4**: User Experience
   - Customization tools
   - Project management
   - Export functionality

5. **Phase 5**: Monetization
   - Stripe integration
   - Subscription management
   - Usage tracking

## Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, email <EMAIL> or join our Discord community.
