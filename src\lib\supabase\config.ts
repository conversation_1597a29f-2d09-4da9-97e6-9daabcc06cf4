// Supabase configuration and helper functions

import { Database } from '@/types/database';
import { createClient } from './client';
import { createClient as createServerClient } from './server';

// Type helpers
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row'];
export type Enums<T extends keyof Database['public']['Enums']> = Database['public']['Enums'][T];

// Table type aliases for easier use
export type Profile = Tables<'profiles'>;
export type Project = Tables<'projects'>;
export type Template = Tables<'templates'>;
export type Generation = Tables<'generations'>;
export type UsageTracking = Tables<'usage_tracking'>;

// Enum type aliases
export type SubscriptionTier = Enums<'subscription_tier'>;
export type SubscriptionStatus = Enums<'subscription_status'>;
export type ProjectStatus = Enums<'project_status'>;
export type TemplateCategory = Enums<'template_category'>;
export type GenerationStatus = Enums<'generation_status'>;
export type UsageAction = Enums<'usage_action'>;

// Storage bucket names
export const STORAGE_BUCKETS = {
  ORIGINAL_IMAGES: 'original-images',
  MOCKUP_IMAGES: 'mockup-images',
  TEMPLATE_PREVIEWS: 'template-previews',
  AVATARS: 'avatars',
} as const;

// Helper functions for common operations
export class SupabaseHelpers {
  private supabase: ReturnType<typeof createClient>;

  constructor(supabaseClient?: ReturnType<typeof createClient>) {
    this.supabase = supabaseClient || createClient();
  }

  // Profile operations
  async getProfile(userId: string) {
    if (!this.supabase) return null;
    
    const { data, error } = await this.supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching profile:', error);
      return null;
    }

    return data;
  }

  async updateProfile(userId: string, updates: Partial<Profile>) {
    if (!this.supabase) return null;

    const { data, error } = await this.supabase
      .from('profiles')
      .update(updates)
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      console.error('Error updating profile:', error);
      return null;
    }

    return data;
  }

  // Project operations
  async getUserProjects(userId: string, limit = 10, offset = 0) {
    if (!this.supabase) return [];

    const { data, error } = await this.supabase
      .from('projects')
      .select(`
        *,
        templates (
          name,
          category,
          preview_image_url
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching projects:', error);
      return [];
    }

    return data || [];
  }

  async createProject(project: Database['public']['Tables']['projects']['Insert']) {
    if (!this.supabase) return null;

    const { data, error } = await this.supabase
      .from('projects')
      .insert(project)
      .select()
      .single();

    if (error) {
      console.error('Error creating project:', error);
      return null;
    }

    return data;
  }

  async updateProject(projectId: string, updates: Partial<Project>) {
    if (!this.supabase) return null;

    const { data, error } = await this.supabase
      .from('projects')
      .update(updates)
      .eq('id', projectId)
      .select()
      .single();

    if (error) {
      console.error('Error updating project:', error);
      return null;
    }

    return data;
  }

  // Template operations
  async getTemplates(category?: TemplateCategory, isPremium?: boolean) {
    if (!this.supabase) return [];

    let query = this.supabase
      .from('templates')
      .select('*')
      .order('created_at', { ascending: false });

    if (category) {
      query = query.eq('category', category);
    }

    if (isPremium !== undefined) {
      query = query.eq('is_premium', isPremium);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching templates:', error);
      return [];
    }

    return data || [];
  }

  async getTemplate(templateId: string) {
    if (!this.supabase) return null;

    const { data, error } = await this.supabase
      .from('templates')
      .select('*')
      .eq('id', templateId)
      .single();

    if (error) {
      console.error('Error fetching template:', error);
      return null;
    }

    return data;
  }

  // Usage tracking
  async trackUsage(userId: string, action: UsageAction, metadata?: any) {
    if (!this.supabase) return null;

    const { data, error } = await this.supabase
      .from('usage_tracking')
      .insert({
        user_id: userId,
        action,
        metadata,
      })
      .select()
      .single();

    if (error) {
      console.error('Error tracking usage:', error);
      return null;
    }

    return data;
  }

  async getMonthlyUsage(userId: string, action: UsageAction) {
    if (!this.supabase) return 0;

    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const { count, error } = await this.supabase
      .from('usage_tracking')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('action', action)
      .gte('created_at', startOfMonth.toISOString());

    if (error) {
      console.error('Error fetching usage:', error);
      return 0;
    }

    return count || 0;
  }

  // File upload helpers
  async uploadFile(bucket: string, path: string, file: File) {
    if (!this.supabase) return null;

    const { data, error } = await this.supabase.storage
      .from(bucket)
      .upload(path, file, {
        cacheControl: '3600',
        upsert: false,
      });

    if (error) {
      console.error('Error uploading file:', error);
      return null;
    }

    return data;
  }

  async getPublicUrl(bucket: string, path: string) {
    if (!this.supabase) return null;

    const { data } = this.supabase.storage
      .from(bucket)
      .getPublicUrl(path);

    return data.publicUrl;
  }

  async deleteFile(bucket: string, path: string) {
    if (!this.supabase) return false;

    const { error } = await this.supabase.storage
      .from(bucket)
      .remove([path]);

    if (error) {
      console.error('Error deleting file:', error);
      return false;
    }

    return true;
  }
}

// Export a default instance
export const supabaseHelpers = new SupabaseHelpers();
